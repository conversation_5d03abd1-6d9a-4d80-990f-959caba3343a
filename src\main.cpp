#include <Arduino.h>
#include <TFT_eSPI.h>

// Include LVGL configuration first
#include "lv_conf.h"
#include <lvgl.h>

// Display and touch
TFT_eSPI tft = TFT_eSPI();

// LVGL display buffer - increase buffer size
static lv_disp_draw_buf_t draw_buf;
static lv_color_t buf[240 * 20];  // Increased buffer size

// LVGL display driver
static lv_disp_drv_t disp_drv;

// LVGL input device driver
static lv_indev_drv_t indev_drv;

// Solar panel data structure
struct SolarData {
    float voltage = 12.5;
    float current = 2.3;
    float power = 28.75;
    float energy_today = 145.2;
    float efficiency = 85.6;
    int battery_level = 78;
    bool is_charging = true;
    float temperature = 32.5;
};

SolarData solar_data;

// UI objects
lv_obj_t *main_screen;
lv_obj_t *voltage_label;
lv_obj_t *current_label;
lv_obj_t *power_label;
lv_obj_t *energy_label;
lv_obj_t *efficiency_bar;
lv_obj_t *battery_bar;
lv_obj_t *temp_label;
lv_obj_t *status_label;
lv_obj_t *power_arc;

// Display flush callback
void my_disp_flush(lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p) {
    uint32_t w = (area->x2 - area->x1 + 1);
    uint32_t h = (area->y2 - area->y1 + 1);

    // Debug output
    static int flush_count = 0;
    if (flush_count < 5) {
        Serial.printf("Flush %d: area(%d,%d,%d,%d) size=%dx%d\n",
                     flush_count, area->x1, area->y1, area->x2, area->y2, w, h);
        flush_count++;
    }

    tft.startWrite();
    tft.setAddrWindow(area->x1, area->y1, w, h);
    tft.pushColors((uint16_t*)color_p, w * h, true);
    tft.endWrite();

    lv_disp_flush_ready(disp);
}

// Touch read callback
void my_touchpad_read(lv_indev_drv_t *indev_driver, lv_indev_data_t *data) {
    uint16_t touchX, touchY;
    bool touched = false;

    #ifdef TOUCH_CS
    touched = tft.getTouch(&touchX, &touchY);
    #endif

    if (touched) {
        data->state = LV_INDEV_STATE_PR;
        data->point.x = touchX;
        data->point.y = touchY;
    } else {
        data->state = LV_INDEV_STATE_REL;
    }
}

// Create gradient style
void create_gradient_style(lv_style_t *style, lv_color_t color1, lv_color_t color2) {
    lv_style_init(style);
    lv_style_set_bg_opa(style, LV_OPA_COVER);
    lv_style_set_bg_color(style, color1);
    lv_style_set_bg_grad_color(style, color2);
    lv_style_set_bg_grad_dir(style, LV_GRAD_DIR_VER);
}

// Create card container
lv_obj_t* create_card(lv_obj_t *parent, lv_coord_t x, lv_coord_t y, lv_coord_t w, lv_coord_t h) {
    lv_obj_t *card = lv_obj_create(parent);
    lv_obj_set_size(card, w, h);
    lv_obj_set_pos(card, x, y);
    lv_obj_set_style_radius(card, 10, 0);
    lv_obj_set_style_shadow_width(card, 5, 0);
    lv_obj_set_style_shadow_opa(card, LV_OPA_30, 0);
    lv_obj_set_style_bg_color(card, lv_color_white(), 0);
    lv_obj_set_style_border_width(card, 1, 0);
    lv_obj_set_style_border_color(card, lv_color_hex(0xE0E0E0), 0);
    return card;
}

// Create value display with icon
void create_value_display(lv_obj_t *parent, const char *icon, const char *label_text,
                         lv_obj_t **value_label, lv_color_t color) {
    // Icon
    lv_obj_t *icon_label = lv_label_create(parent);
    lv_label_set_text(icon_label, icon);
    lv_obj_set_style_text_color(icon_label, color, 0);
    lv_obj_set_style_text_font(icon_label, &lv_font_montserrat_14, 0);
    lv_obj_align(icon_label, LV_ALIGN_TOP_LEFT, 5, 5);

    // Title
    lv_obj_t *title = lv_label_create(parent);
    lv_label_set_text(title, label_text);
    lv_obj_set_style_text_color(title, lv_color_hex(0x666666), 0);
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
    lv_obj_align_to(title, icon_label, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

    // Value
    *value_label = lv_label_create(parent);
    lv_obj_set_style_text_color(*value_label, lv_color_hex(0x333333), 0);
    lv_obj_set_style_text_font(*value_label, &lv_font_montserrat_14, 0);
    lv_obj_align(*value_label, LV_ALIGN_BOTTOM_LEFT, 5, -5);
}

void setup() {
    Serial.begin(115200);
    Serial.println("Starting Solar Dashboard...");

    // Initialize display
    tft.init();
    tft.setRotation(0); // Portrait mode

    // Test display with colors
    tft.fillScreen(TFT_RED);
    delay(500);
    tft.fillScreen(TFT_GREEN);
    delay(500);
    tft.fillScreen(TFT_BLUE);
    delay(500);
    tft.fillScreen(TFT_BLACK);

    Serial.println("Display initialized and tested");

    // Initialize touch with better calibration
    #ifdef TOUCH_CS
    uint16_t calData[5] = { 275, 3620, 264, 3532, 1 };
    tft.setTouch(calData);
    #endif

    // Add delay for stability
    delay(100);

    // Initialize LVGL
    lv_init();
    Serial.println("LVGL initialized");

    // Initialize display buffer
    lv_disp_draw_buf_init(&draw_buf, buf, NULL, 240 * 20);

    // Initialize display driver
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = 240;
    disp_drv.ver_res = 320;
    disp_drv.flush_cb = my_disp_flush;
    disp_drv.draw_buf = &draw_buf;
    lv_disp_t *disp = lv_disp_drv_register(&disp_drv);

    // Initialize input device driver
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = my_touchpad_read;
    lv_indev_drv_register(&indev_drv);

    // Create main screen
    main_screen = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(main_screen, lv_color_hex(0xF5F5F5), 0);

    // Title
    lv_obj_t *title = lv_label_create(main_screen);
    lv_label_set_text(title, "Solar Dashboard");
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(title, lv_color_hex(0x2E7D32), 0);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 10);

    // Power arc (main display)
    power_arc = lv_arc_create(main_screen);
    lv_obj_set_size(power_arc, 120, 120);
    lv_obj_align(power_arc, LV_ALIGN_TOP_MID, 0, 35);
    lv_arc_set_range(power_arc, 0, 100);
    lv_arc_set_value(power_arc, 75);
    lv_obj_set_style_arc_color(power_arc, lv_color_hex(0x4CAF50), LV_PART_INDICATOR);
    lv_obj_set_style_arc_width(power_arc, 8, LV_PART_INDICATOR);
    lv_obj_set_style_arc_width(power_arc, 8, LV_PART_MAIN);
    lv_obj_remove_style(power_arc, NULL, LV_PART_KNOB);

    // Power value in center of arc
    lv_obj_t *power_center = lv_label_create(main_screen);
    lv_label_set_text(power_center, "28.8W");
    lv_obj_set_style_text_font(power_center, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(power_center, lv_color_hex(0x2E7D32), 0);
    lv_obj_align_to(power_center, power_arc, LV_ALIGN_CENTER, 0, -5);

    lv_obj_t *power_label_center = lv_label_create(main_screen);
    lv_label_set_text(power_label_center, "Power");
    lv_obj_set_style_text_font(power_label_center, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(power_label_center, lv_color_hex(0x666666), 0);
    lv_obj_align_to(power_label_center, power_center, LV_ALIGN_OUT_BOTTOM_MID, 0, 2);

    // Voltage card
    lv_obj_t *voltage_card = create_card(main_screen, 10, 170, 105, 60);
    create_value_display(voltage_card, "⚡", "Voltage", &voltage_label, lv_color_hex(0xFF9800));

    // Current card
    lv_obj_t *current_card = create_card(main_screen, 125, 170, 105, 60);
    create_value_display(current_card, "⚡", "Current", &current_label, lv_color_hex(0x2196F3));

    // Battery level bar
    lv_obj_t *battery_card = create_card(main_screen, 10, 240, 220, 35);
    lv_obj_t *battery_title = lv_label_create(battery_card);
    lv_label_set_text(battery_title, "Battery");
    lv_obj_set_style_text_font(battery_title, &lv_font_montserrat_14, 0);
    lv_obj_align(battery_title, LV_ALIGN_TOP_LEFT, 5, 2);

    battery_bar = lv_bar_create(battery_card);
    lv_obj_set_size(battery_bar, 180, 8);
    lv_obj_align(battery_bar, LV_ALIGN_BOTTOM_LEFT, 5, -5);
    lv_bar_set_range(battery_bar, 0, 100);
    lv_obj_set_style_bg_color(battery_bar, lv_color_hex(0x4CAF50), LV_PART_INDICATOR);

    // Status and temperature
    status_label = lv_label_create(main_screen);
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_14, 0);
    lv_obj_align(status_label, LV_ALIGN_BOTTOM_LEFT, 10, -10);

    temp_label = lv_label_create(main_screen);
    lv_obj_set_style_text_font(temp_label, &lv_font_montserrat_14, 0);
    lv_obj_align(temp_label, LV_ALIGN_BOTTOM_RIGHT, -10, -10);

    Serial.println("UI created, loading screen...");

    // Load the screen
    lv_scr_load(main_screen);

    // Force a refresh
    lv_obj_invalidate(main_screen);

    Serial.println("Screen loaded, forcing refresh...");

    Serial.println("Solar Dashboard initialized!");
}

void update_solar_data() {
    // Simulate changing data (in real application, read from sensors)
    solar_data.voltage = 12.0 + (millis() % 5000) / 1000.0;
    solar_data.current = 2.0 + (millis() % 3000) / 3000.0;
    solar_data.power = solar_data.voltage * solar_data.current;
    solar_data.energy_today += solar_data.power / 3600000.0; // Wh
    solar_data.efficiency = 80 + (millis() % 2000) / 100.0;
    solar_data.battery_level = 70 + (millis() % 3000) / 100;
    if (solar_data.battery_level > 100) solar_data.battery_level = 100;
    solar_data.temperature = 30 + (millis() % 1000) / 100.0;
    solar_data.is_charging = solar_data.power > 20;
}

void update_ui() {
    // Update voltage
    char voltage_text[20];
    sprintf(voltage_text, "%.1fV", solar_data.voltage);
    lv_label_set_text(voltage_label, voltage_text);

    // Update current
    char current_text[20];
    sprintf(current_text, "%.1fA", solar_data.current);
    lv_label_set_text(current_label, current_text);

    // Update power arc
    int power_percentage = (int)(solar_data.power * 100 / 50); // Assuming max 50W
    if (power_percentage > 100) power_percentage = 100;
    lv_arc_set_value(power_arc, power_percentage);

    // Update battery
    lv_bar_set_value(battery_bar, solar_data.battery_level, LV_ANIM_ON);

    // Update status
    char status_text[50];
    sprintf(status_text, "%s %.1fkWh",
            solar_data.is_charging ? "🔋 Charging" : "⏸️ Standby",
            solar_data.energy_today / 1000.0);
    lv_label_set_text(status_label, status_text);

    // Update temperature
    char temp_text[20];
    sprintf(temp_text, "🌡️ %.1f°C", solar_data.temperature);
    lv_label_set_text(temp_label, temp_text);
}

void loop() {
    static unsigned long last_update = 0;
    static unsigned long last_lvgl_update = 0;

    // Update LVGL more frequently for smooth display
    if (millis() - last_lvgl_update >= 5) {
        lv_timer_handler();
        last_lvgl_update = millis();
    }

    // Update data every 2 seconds
    if (millis() - last_update > 2000) {
        update_solar_data();
        update_ui();
        last_update = millis();
    }

    delay(1);
}